/**
 * 编辑器状态切片
 */
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { apiClient } from '../../services/ApiClient';

// 定义变换模式
export enum TransformMode {
  TRANSLATE = 'translate',
  ROTATE = 'rotate',
  SCALE = 'scale'
}

// 定义坐标空间
export enum TransformSpace {
  LOCAL = 'local',
  WORLD = 'world'
}

// 定义网格捕捉模式
export enum SnapMode {
  DISABLED = 'disabled',
  GRID = 'grid',
  VERTEX = 'vertex'
}

// 定义编辑器状态
interface EditorState {
  isLoading: boolean;
  error: string | null;
  sceneData: any | null; // 场景数据
  activeCamera: any | null; // 使用any类型，实际应该是Camera类型
  selectedObject: any | null; // 使用any类型，实际应该是Object3D类型
  selectedObjects: any[]; // 使用any类型，实际应该是Object3D[]类型
  transformMode: TransformMode;
  transformSpace: TransformSpace;
  snapMode: SnapMode;
  gridSize: number;
  showGrid: boolean;
  showAxes: boolean;
  showStats: boolean;
  undoStack: any[];
  redoStack: any[];
  isPlaying: boolean;
  viewportSize: { width: number; height: number };
  sceneGraph: any[]; // 场景图层次结构
}

// 初始状态
const initialState: EditorState = {
  isLoading: false,
  error: null,
  sceneData: null,
  activeCamera: null,
  selectedObject: null,
  selectedObjects: [],
  transformMode: TransformMode.TRANSLATE,
  transformSpace: TransformSpace.LOCAL,
  snapMode: SnapMode.DISABLED,
  gridSize: 1,
  showGrid: true,
  showAxes: true,
  showStats: false,
  undoStack: [],
  redoStack: [],
  isPlaying: false,
  viewportSize: { width: 0, height: 0 },
  sceneGraph: []
};

// 加载场景
export const loadScene = createAsyncThunk(
  'editor/loadScene',
  async ({ sceneId, projectId }: { sceneId: string; projectId: string }, { rejectWithValue }) => {
    try {
      console.log('开始加载场景数据:', { projectId, sceneId });
      const response = await apiClient.get(`/projects/${projectId}/scenes/${sceneId}/data`);
      console.log('场景数据加载成功:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('加载场景数据失败:', error);

      // 提供更详细的错误信息
      if (!error.response) {
        return rejectWithValue('网络连接失败，请检查网络连接');
      }

      if (error.response.status === 401) {
        return rejectWithValue('登录已过期，请重新登录');
      }

      if (error.response.status === 403) {
        return rejectWithValue('权限不足，无法访问此场景');
      }

      if (error.response.status === 404) {
        return rejectWithValue('场景不存在或已被删除');
      }

      if (error.response.status >= 500) {
        return rejectWithValue('服务暂时不可用，请稍后重试');
      }

      return rejectWithValue(error.response?.data?.message || '加载场景失败');
    }
  }
);

// 保存场景
export const saveScene = createAsyncThunk(
  'editor/saveScene',
  async (
    { sceneId, projectId, data }: { sceneId: string; projectId: string; data: any },
    { rejectWithValue }
  ) => {
    try {
      console.log('开始保存场景数据:', { projectId, sceneId });
      const response = await apiClient.put(`/projects/${projectId}/scenes/${sceneId}/data`, data);
      console.log('场景数据保存成功:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('保存场景数据失败:', error);

      // 提供更详细的错误信息
      if (!error.response) {
        return rejectWithValue('网络连接失败，请检查网络连接');
      }

      if (error.response.status === 401) {
        return rejectWithValue('登录已过期，请重新登录');
      }

      if (error.response.status === 403) {
        return rejectWithValue('权限不足，无法保存此场景');
      }

      if (error.response.status >= 500) {
        return rejectWithValue('服务暂时不可用，请稍后重试');
      }

      return rejectWithValue(error.response?.data?.message || '保存场景失败');
    }
  }
);

// 创建编辑器切片
const editorSlice = createSlice({
  name: 'editor',
  initialState,
  reducers: {
    setActiveCamera: (state, action: PayloadAction<any>) => {
      state.activeCamera = action.payload;
    },
    setSelectedObject: (state, action: PayloadAction<any>) => {
      state.selectedObject = action.payload;
      state.selectedObjects = action.payload ? [action.payload] : [];
    },
    setSelectedObjects: (state, action: PayloadAction<any[]>) => {
      state.selectedObjects = action.payload;
      state.selectedObject = action.payload.length === 1 ? action.payload[0] : null;
    },
    setTransformMode: (state, action: PayloadAction<TransformMode>) => {
      state.transformMode = action.payload;
    },
    setTransformSpace: (state, action: PayloadAction<TransformSpace>) => {
      state.transformSpace = action.payload;
    },
    setSnapMode: (state, action: PayloadAction<SnapMode>) => {
      state.snapMode = action.payload;
    },
    setGridSize: (state, action: PayloadAction<number>) => {
      state.gridSize = action.payload;
    },
    setShowGrid: (state, action: PayloadAction<boolean>) => {
      state.showGrid = action.payload;
    },
    setShowAxes: (state, action: PayloadAction<boolean>) => {
      state.showAxes = action.payload;
    },
    setShowStats: (state, action: PayloadAction<boolean>) => {
      state.showStats = action.payload;
    },
    addToUndoStack: (state, action: PayloadAction<any>) => {
      state.undoStack.push(action.payload);
      // 限制撤销栈大小
      if (state.undoStack.length > 50) {
        state.undoStack.shift();
      }
    },
    undo: (state) => {
      if (state.undoStack.length > 0) {
        const action = state.undoStack.pop();
        if (action) {
          state.redoStack.push(action);
        }
      }
    },
    redo: (state) => {
      if (state.redoStack.length > 0) {
        const action = state.redoStack.pop();
        if (action) {
          state.undoStack.push(action);
        }
      }
    },
    clearUndoRedoStack: (state) => {
      state.undoStack = [];
      state.redoStack = [];
    },
    setIsPlaying: (state, action: PayloadAction<boolean>) => {
      state.isPlaying = action.payload;
    },
    setViewportSize: (state, action: PayloadAction<{ width: number; height: number }>) => {
      state.viewportSize = action.payload;
    },
    setSceneGraph: (state, action: PayloadAction<any[]>) => {
      state.sceneGraph = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    // 加载场景
    builder
      .addCase(loadScene.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadScene.fulfilled, (state, action) => {
        state.isLoading = false;
        // 处理场景数据
        const sceneData = action.payload;
        if (sceneData) {
          // 存储场景数据到编辑器状态
          state.sceneData = sceneData;
          console.log('场景数据加载成功:', sceneData);
        }
      })
      .addCase(loadScene.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 保存场景
    builder
      .addCase(saveScene.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(saveScene.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(saveScene.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  }
});

export const {
  setActiveCamera,
  setSelectedObject,
  setSelectedObjects,
  setTransformMode,
  setTransformSpace,
  setSnapMode,
  setGridSize,
  setShowGrid,
  setShowAxes,
  setShowStats,
  addToUndoStack,
  undo,
  redo,
  clearUndoRedoStack,
  setIsPlaying,
  setViewportSize,
  setSceneGraph,
  clearError
} = editorSlice.actions;

export default editorSlice.reducer;
