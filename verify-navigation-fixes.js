#!/usr/bin/env node
/**
 * 验证项目管理到编辑器跳转功能修复
 * 检查修复的代码是否正确实现
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, colors.green);
}

function error(message) {
  log(`❌ ${message}`, colors.red);
}

function warning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function info(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    success(`${description} 存在: ${filePath}`);
    return true;
  } else {
    error(`${description} 不存在: ${filePath}`);
    return false;
  }
}

/**
 * 检查文件内容
 */
function checkFileContent(filePath, pattern, description) {
  try {
    if (!fs.existsSync(filePath)) {
      error(`${description} - 文件不存在: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
    
    if (regex.test(content)) {
      success(`${description} - 检查通过`);
      return true;
    } else {
      error(`${description} - 检查失败: 未找到匹配内容`);
      return false;
    }
  } catch (err) {
    error(`${description} - 检查失败: ${err.message}`);
    return false;
  }
}

/**
 * 主验证流程
 */
function runVerification() {
  console.log('🔍 开始验证项目管理到编辑器跳转功能修复\n');

  let passedTests = 0;
  let totalTests = 0;

  function test(description, testFn) {
    totalTests++;
    if (testFn()) {
      passedTests++;
    }
  }

  // 1. 验证editorSlice修复
  info('1. 验证editorSlice修复...');
  
  test('editorSlice导入apiClient', () => 
    checkFileContent(
      'editor/src/store/editor/editorSlice.ts',
      /import.*apiClient.*from.*\.\.\/\.\.\/services\/ApiClient/,
      'editorSlice导入apiClient'
    )
  );

  test('editorSlice使用apiClient而非axios', () => 
    checkFileContent(
      'editor/src/store/editor/editorSlice.ts',
      /const response = await apiClient\.get\(/,
      'editorSlice使用apiClient'
    )
  );

  test('editorSlice不再直接使用axios', () => {
    const content = fs.readFileSync('editor/src/store/editor/editorSlice.ts', 'utf8');
    if (!content.includes('import axios from') && !content.includes('await axios.get')) {
      success('editorSlice不再直接使用axios');
      return true;
    } else {
      error('editorSlice仍在使用axios');
      return false;
    }
  });

  // 2. 验证Docker配置一致性
  info('\n2. 验证Docker配置一致性...');
  
  test('docker-compose.yml API URL配置', () => 
    checkFileContent(
      'docker-compose.windows.yml',
      /REACT_APP_API_URL=\/api/,
      'docker-compose.yml API URL配置'
    )
  );

  test('docker-compose.yml WebSocket URL配置', () => 
    checkFileContent(
      'docker-compose.windows.yml',
      /REACT_APP_COLLABORATION_SERVER_URL=\/ws/,
      'docker-compose.yml WebSocket URL配置'
    )
  );

  test('Dockerfile API URL配置', () => 
    checkFileContent(
      'editor/Dockerfile',
      /ENV REACT_APP_API_URL=\/api/,
      'Dockerfile API URL配置'
    )
  );

  // 3. 验证前端路由配置
  info('\n3. 验证前端路由配置...');
  
  test('App.tsx编辑器路由', () => 
    checkFileContent(
      'editor/src/App.tsx',
      /<Route path="editor\/:projectId\/:sceneId" element={<EditorPage \/>/,
      'App.tsx编辑器路由配置'
    )
  );

  // 4. 验证项目页面handleOpenProject函数
  info('\n4. 验证项目页面handleOpenProject函数...');
  
  test('ProjectsPage handleOpenProject函数', () => 
    checkFileContent(
      'editor/src/pages/ProjectsPage.tsx',
      /const handleOpenProject = async \(project: any\) => \{/,
      'ProjectsPage handleOpenProject函数定义'
    )
  );

  test('ProjectsPage navigate调用', () => 
    checkFileContent(
      'editor/src/pages/ProjectsPage.tsx',
      /navigate\(`\/editor\/\$\{project\.id\}\/\$\{scene\.id\}`\)/,
      'ProjectsPage navigate调用'
    )
  );

  // 5. 验证EditorPage初始化
  info('\n5. 验证EditorPage初始化...');
  
  test('EditorPage loadScene调用', () => 
    checkFileContent(
      'editor/src/pages/EditorPage.tsx',
      /await dispatch\(loadScene\(\{ projectId, sceneId \}\)\)\.unwrap\(\)/,
      'EditorPage loadScene调用'
    )
  );

  // 6. 验证后端API接口
  info('\n6. 验证后端API接口...');
  
  test('API网关场景数据接口', () => 
    checkFileContent(
      'server/api-gateway/src/projects/scenes.controller.ts',
      /@Get\(':id\/data'\)/,
      'API网关场景数据接口'
    )
  );

  test('API网关getSceneData方法', () => 
    checkFileContent(
      'server/api-gateway/src/projects/scenes.controller.ts',
      /async getSceneData\(@Param\('id'\) id: string, @Request\(\) req\)/,
      'API网关getSceneData方法'
    )
  );

  // 7. 验证nginx配置
  info('\n7. 验证nginx配置...');
  
  test('nginx API代理配置', () => 
    checkFileContent(
      'editor/nginx.conf',
      /location \/api\//,
      'nginx API代理配置'
    )
  );

  test('nginx API代理目标', () => 
    checkFileContent(
      'editor/nginx.conf',
      /set \$api_backend "http:\/\/api-gateway:3000"/,
      'nginx API代理目标'
    )
  );

  // 输出结果
  console.log('\n📊 验证结果:');
  success(`✅ 通过: ${passedTests}/${totalTests} 项检查`);
  
  if (passedTests < totalTests) {
    error(`❌ 失败: ${totalTests - passedTests}/${totalTests} 项检查`);
  }
  
  const successRate = ((passedTests / totalTests) * 100).toFixed(1);
  info(`📈 成功率: ${successRate}%`);

  if (passedTests === totalTests) {
    console.log('\n🎉 所有检查都通过了！项目管理到编辑器跳转功能修复完成。');
    console.log('\n📋 修复总结:');
    success('1. ✅ 修复了editorSlice中使用axios而非apiClient的问题');
    success('2. ✅ 统一了Docker配置中的API URL设置');
    success('3. ✅ 验证了前端路由配置正确');
    success('4. ✅ 确认了项目页面的handleOpenProject函数实现');
    success('5. ✅ 验证了EditorPage的初始化逻辑');
    success('6. ✅ 确认了后端API接口完整');
    success('7. ✅ 验证了nginx代理配置正确');
    
    console.log('\n🚀 下一步操作:');
    info('1. 重新构建并启动Docker服务');
    info('2. 在浏览器中访问 http://localhost:80');
    info('3. 登录系统并进入项目管理页面');
    info('4. 点击项目卡片测试跳转功能');
    info('5. 验证编辑器界面是否正确加载项目数据');
  } else {
    console.log('\n⚠️  部分检查未通过，请检查上述失败项目并进行修复。');
  }
}

// 运行验证
if (require.main === module) {
  runVerification();
}

module.exports = { runVerification };
