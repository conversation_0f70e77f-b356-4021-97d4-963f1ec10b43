#!/usr/bin/env node
/**
 * 修复所有axios导入和使用，替换为apiClient
 */

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'editor/src/services/materialService.ts',
  'editor/src/services/animationService.ts'
];

function fixAxiosUsage(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;

    // 替换axios导入为apiClient导入
    if (content.includes("import axios from 'axios';")) {
      content = content.replace(
        /import axios from 'axios';\s*\n/g,
        ''
      );
      
      // 添加apiClient导入（如果还没有）
      if (!content.includes("import { apiClient } from './ApiClient';")) {
        const importMatch = content.match(/^(import[^;]+;[\s\n]*)+/);
        if (importMatch) {
          const importSection = importMatch[0];
          const newImportSection = importSection + "import { apiClient } from './ApiClient';\n";
          content = content.replace(importSection, newImportSection);
        }
      }
      changed = true;
    }

    // 移除API_URL常量定义
    content = content.replace(/const API_URL = [^;]+;\s*\n/g, '');

    // 替换axios.get调用
    content = content.replace(
      /await axios\.get\(`\$\{API_URL\}([^`]*)`([^)]*)\)/g,
      "await apiClient.get('$1'$2)"
    );

    content = content.replace(
      /await axios\.get\(API_URL([^)]*)\)/g,
      "await apiClient.get('/materials'$1)"
    );

    // 替换axios.post调用
    content = content.replace(
      /await axios\.post\(`\$\{API_URL\}([^`]*)`([^)]*)\)/g,
      "await apiClient.post('$1'$2)"
    );

    content = content.replace(
      /await axios\.post\(API_URL([^)]*)\)/g,
      "await apiClient.post('/materials'$1)"
    );

    // 替换axios.patch调用
    content = content.replace(
      /await axios\.patch\(`\$\{API_URL\}([^`]*)`([^)]*)\)/g,
      "await apiClient.patch('$1'$2)"
    );

    // 替换axios.delete调用
    content = content.replace(
      /await axios\.delete\(`\$\{API_URL\}([^`]*)`([^)]*)\)/g,
      "await apiClient.delete('$1'$2)"
    );

    content = content.replace(
      /await axios\.delete\(API_URL([^)]*)\)/g,
      "await apiClient.delete('/materials'$1)"
    );

    // 特殊处理文件上传
    content = content.replace(
      /await axios\.post\(`\$\{API_URL\}\/textures\/upload`[^}]+\}/g,
      "await apiClient.upload('/materials/textures/upload', file)"
    );

    if (changed || content !== fs.readFileSync(filePath, 'utf8')) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
    } else {
      console.log(`ℹ️  无需修复: ${filePath}`);
    }

  } catch (error) {
    console.error(`❌ 修复失败 ${filePath}:`, error.message);
  }
}

// 修复所有文件
console.log('🔧 开始修复axios使用...\n');

filesToFix.forEach(fixAxiosUsage);

console.log('\n🎉 修复完成！');
