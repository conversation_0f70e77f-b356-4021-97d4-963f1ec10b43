#!/usr/bin/env node
/**
 * 验证构建修复
 * 检查所有axios相关的修复是否正确实现
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, colors.green);
}

function error(message) {
  log(`❌ ${message}`, colors.red);
}

function warning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function info(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    success(`${description} 存在: ${filePath}`);
    return true;
  } else {
    error(`${description} 不存在: ${filePath}`);
    return false;
  }
}

/**
 * 检查文件内容
 */
function checkFileContent(filePath, pattern, description) {
  try {
    if (!fs.existsSync(filePath)) {
      error(`${description} - 文件不存在: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
    
    if (regex.test(content)) {
      success(`${description} - 检查通过`);
      return true;
    } else {
      error(`${description} - 检查失败: 未找到匹配内容`);
      return false;
    }
  } catch (err) {
    error(`${description} - 检查失败: ${err.message}`);
    return false;
  }
}

/**
 * 检查文件中不包含特定内容
 */
function checkFileNotContains(filePath, pattern, description) {
  try {
    if (!fs.existsSync(filePath)) {
      error(`${description} - 文件不存在: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
    
    if (!regex.test(content)) {
      success(`${description} - 检查通过`);
      return true;
    } else {
      error(`${description} - 检查失败: 仍包含不应该存在的内容`);
      return false;
    }
  } catch (err) {
    error(`${description} - 检查失败: ${err.message}`);
    return false;
  }
}

/**
 * 主验证流程
 */
function runVerification() {
  console.log('🔍 开始验证构建修复\n');

  let passedTests = 0;
  let totalTests = 0;

  function test(description, testFn) {
    totalTests++;
    if (testFn()) {
      passedTests++;
    }
  }

  // 1. 验证editorSlice修复
  info('1. 验证editorSlice修复...');
  
  test('editorSlice导入apiClient', () => 
    checkFileContent(
      'editor/src/store/editor/editorSlice.ts',
      /import.*apiClient.*from.*\.\.\/\.\.\/services\/ApiClient/,
      'editorSlice导入apiClient'
    )
  );

  test('editorSlice不再使用axios', () => 
    checkFileNotContains(
      'editor/src/store/editor/editorSlice.ts',
      /import axios|await axios\./,
      'editorSlice不再使用axios'
    )
  );

  // 2. 验证assetSlice修复
  info('\n2. 验证assetSlice修复...');
  
  test('assetSlice导入apiClient', () => 
    checkFileContent(
      'editor/src/store/asset/assetSlice.ts',
      /import.*apiClient.*from.*\.\.\/\.\.\/services\/ApiClient/,
      'assetSlice导入apiClient'
    )
  );

  test('assetSlice不再使用axios', () => 
    checkFileNotContains(
      'editor/src/store/asset/assetSlice.ts',
      /import axios|await axios\./,
      'assetSlice不再使用axios'
    )
  );

  // 3. 验证materialService修复
  info('\n3. 验证materialService修复...');
  
  test('materialService文件存在', () => 
    checkFileExists('editor/src/services/materialService.ts', 'materialService文件')
  );

  test('materialService导入apiClient', () => 
    checkFileContent(
      'editor/src/services/materialService.ts',
      /import.*apiClient.*from.*\.\/ApiClient/,
      'materialService导入apiClient'
    )
  );

  test('materialService不再使用axios', () => 
    checkFileNotContains(
      'editor/src/services/materialService.ts',
      /import axios|await axios\./,
      'materialService不再使用axios'
    )
  );

  // 4. 验证animationService修复
  info('\n4. 验证animationService修复...');
  
  test('animationService导入apiClient', () => 
    checkFileContent(
      'editor/src/services/animationService.ts',
      /import.*apiClient.*from.*\.\/ApiClient/,
      'animationService导入apiClient'
    )
  );

  test('animationService不再使用axios', () => 
    checkFileNotContains(
      'editor/src/services/animationService.ts',
      /import axios|await axios\./,
      'animationService不再使用axios'
    )
  );

  // 5. 验证Docker配置一致性
  info('\n5. 验证Docker配置一致性...');
  
  test('docker-compose.yml API URL配置', () => 
    checkFileContent(
      'docker-compose.windows.yml',
      /REACT_APP_API_URL=\/api/,
      'docker-compose.yml API URL配置'
    )
  );

  test('Dockerfile API URL配置', () => 
    checkFileContent(
      'editor/Dockerfile',
      /ENV REACT_APP_API_URL=\/api/,
      'Dockerfile API URL配置'
    )
  );

  // 输出结果
  console.log('\n📊 验证结果:');
  success(`✅ 通过: ${passedTests}/${totalTests} 项检查`);
  
  if (passedTests < totalTests) {
    error(`❌ 失败: ${totalTests - passedTests}/${totalTests} 项检查`);
  }
  
  const successRate = ((passedTests / totalTests) * 100).toFixed(1);
  info(`📈 成功率: ${successRate}%`);

  if (passedTests === totalTests) {
    console.log('\n🎉 所有检查都通过了！构建错误修复完成。');
    console.log('\n📋 修复总结:');
    success('1. ✅ 修复了editorSlice中的axios使用问题');
    success('2. ✅ 修复了assetSlice中的axios使用问题');
    success('3. ✅ 重新创建了materialService文件');
    success('4. ✅ 修复了animationService中的axios使用问题');
    success('5. ✅ 统一了Docker配置中的API URL设置');
    
    console.log('\n🚀 下一步操作:');
    info('1. 重新运行Docker构建命令');
    info('2. 验证所有服务是否正常启动');
    info('3. 测试前端应用是否正常工作');
  } else {
    console.log('\n⚠️  部分检查未通过，请检查上述失败项目并进行修复。');
  }
}

// 运行验证
if (require.main === module) {
  runVerification();
}

module.exports = { runVerification };
