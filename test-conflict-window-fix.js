/**
 * 测试冲突窗口修复效果的验证脚本
 * 用于验证登录后不再自动显示冲突窗口，且关闭功能正常工作
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始验证冲突窗口修复效果...\n');

/**
 * 检查文件内容是否包含指定模式
 */
function checkFileContent(filePath, pattern, description) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`❌ ${description}: 文件不存在 - ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const matches = pattern.test(content);
    
    if (matches) {
      console.log(`✅ ${description}: 已修复`);
      return true;
    } else {
      console.log(`❌ ${description}: 未修复`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${description}: 检查失败 - ${error.message}`);
    return false;
  }
}

/**
 * 检查文件内容是否不包含指定模式（用于验证删除）
 */
function checkFileNotContains(filePath, pattern, description) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`❌ ${description}: 文件不存在 - ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const matches = pattern.test(content);
    
    if (!matches) {
      console.log(`✅ ${description}: 已修复`);
      return true;
    } else {
      console.log(`❌ ${description}: 仍存在问题`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${description}: 检查失败 - ${error.message}`);
    return false;
  }
}

console.log('1. 检查testFixes.ts自动创建冲突是否已禁用...');

// 检查testFixes.ts是否已禁用自动创建冲突
checkFileContent(
  'editor/src/utils/testFixes.ts',
  /确保不会自动创建任何测试冲突数据/,
  'testFixes.ts自动创建冲突已禁用'
);

// 检查是否没有自动执行的setTimeout调用runAllTests
checkFileNotContains(
  'editor/src/utils/testFixes.ts',
  /setTimeout\(\s*\(\s*\)\s*=>\s*\{[\s\S]*?runAllTests\(\)[\s\S]*?\}\s*,\s*\d+\s*\)/,
  'testFixes.ts没有自动执行的测试代码'
);

console.log('\n2. 检查ConflictResolutionService默认状态...');

// 检查ConflictResolutionService默认为禁用
checkFileContent(
  'editor/src/services/ConflictResolutionService.ts',
  /private enabled: boolean = false;.*\/\/ 默认禁用/,
  'ConflictResolutionService默认禁用'
);

// 检查自动解决冲突默认禁用
checkFileContent(
  'editor/src/services/ConflictResolutionService.ts',
  /private autoResolveSimpleConflicts: boolean = false;.*\/\/ 默认禁用自动解决/,
  'ConflictResolutionService自动解决默认禁用'
);

console.log('\n3. 检查App.tsx微服务初始化逻辑...');

// 检查App.tsx中的状态清理
checkFileContent(
  'editor/src/App.tsx',
  /dispatch\(clearConflicts\(\)\);/,
  'App.tsx登录时清理冲突状态'
);

// 检查页面路径检查逻辑
checkFileContent(
  'editor/src/App.tsx',
  /const shouldShowConflictPanel = showConflictPanel && isEditorPage;/,
  'App.tsx页面路径检查逻辑'
);

// 检查微服务初始化延迟
checkFileContent(
  'editor/src/App.tsx',
  /const needsMicroservices = currentPath\.includes\('\/editor\/'\)/,
  'App.tsx微服务初始化页面检查'
);

console.log('\n4. 检查冲突面板关闭功能...');

// 检查ConflictPanel关闭按钮
checkFileContent(
  'editor/src/components/collaboration/ConflictPanel.tsx',
  /onClick={handleClose}/,
  'ConflictPanel关闭按钮事件'
);

// 检查ConflictPanel关闭函数
checkFileContent(
  'editor/src/components/collaboration/ConflictPanel.tsx',
  /const handleClose = React\.useCallback\(/,
  'ConflictPanel handleClose使用useCallback'
);

// 检查ConflictResolutionDialog关闭配置
checkFileContent(
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  /closable={true}/,
  'ConflictResolutionDialog可关闭配置'
);

checkFileContent(
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  /maskClosable={true}/,
  'ConflictResolutionDialog遮罩关闭配置'
);

console.log('\n5. 检查配置文件一致性...');

// 检查.env文件前端配置
checkFileContent(
  '.env',
  /REACT_APP_API_URL=http:\/\/localhost:3000\/api/,
  '.env前端API URL配置'
);

// 检查docker-compose.windows.yml编辑器配置
checkFileContent(
  'docker-compose.windows.yml',
  /REACT_APP_API_URL=\/api/,
  'docker-compose.windows.yml编辑器API URL配置'
);

// 检查Dockerfile环境变量
checkFileContent(
  'editor/Dockerfile',
  /ENV REACT_APP_API_URL=\/api/,
  'editor/Dockerfile API URL配置'
);

console.log('\n📊 修复效果验证完成！');
console.log('\n🎯 下一步操作：');
console.log('1. 重新启动服务：.\\start-windows.ps1');
console.log('2. 访问 http://localhost:80 进行登录测试');
console.log('3. 验证登录后不再自动显示冲突窗口');
console.log('4. 如果在编辑器页面看到冲突窗口，测试关闭功能是否正常');

console.log('\n💡 如果需要手动测试冲突功能，可在浏览器控制台运行：');
console.log('window.testConflictFunctions.createTestConflict()');
