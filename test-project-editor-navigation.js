#!/usr/bin/env node
/**
 * 项目管理到编辑器跳转功能测试脚本
 * 测试从项目管理界面点击项目卡片到编辑器界面的完整流程
 */

const http = require('http');
const https = require('https');
const { URL } = require('url');

// 配置
const config = {
  apiBaseUrl: 'http://localhost:3000/api',
  frontendUrl: 'http://localhost:80',
  testUser: {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'test123456'
  }
};

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, colors.green);
}

function error(message) {
  log(`❌ ${message}`, colors.red);
}

function warning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function info(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// 全局变量
let authToken = null;
let testProject = null;
let testScene = null;

/**
 * 简单的HTTP请求函数
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const response = {
            status: res.statusCode,
            data: data ? JSON.parse(data) : null
          };
          resolve(response);
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);

    if (options.data) {
      req.write(JSON.stringify(options.data));
    }

    req.end();
  });
}

/**
 * 测试API连接
 */
async function testApiConnection() {
  info('测试API连接...');
  try {
    const response = await makeRequest(`${config.apiBaseUrl}/health`);
    if (response.status === 200) {
      success('API连接正常');
      return true;
    } else {
      error(`API连接失败: HTTP ${response.status}`);
      return false;
    }
  } catch (err) {
    error(`API连接失败: ${err.message}`);
    return false;
  }
}

/**
 * 用户登录
 */
async function loginUser() {
  info('用户登录...');
  try {
    const response = await axios.post(`${config.apiBaseUrl}/auth/login`, {
      usernameOrEmail: config.testUser.username,
      password: config.testUser.password
    });

    if (response.data && response.data.token) {
      authToken = response.data.token;
      success('用户登录成功');
      return true;
    } else {
      error('登录响应格式错误');
      return false;
    }
  } catch (err) {
    if (err.response?.status === 401) {
      warning('用户不存在或密码错误，尝试注册新用户...');
      return await registerAndLogin();
    } else {
      error(`登录失败: ${err.message}`);
      return false;
    }
  }
}

/**
 * 注册并登录用户
 */
async function registerAndLogin() {
  try {
    info('注册新用户...');
    await axios.post(`${config.apiBaseUrl}/auth/register`, {
      username: config.testUser.username,
      email: config.testUser.email,
      password: config.testUser.password,
      displayName: 'Test User'
    });
    success('用户注册成功');
    
    // 注册后立即登录
    return await loginUser();
  } catch (err) {
    error(`注册失败: ${err.message}`);
    return false;
  }
}

/**
 * 获取项目列表
 */
async function getProjects() {
  info('获取项目列表...');
  try {
    const response = await axios.get(`${config.apiBaseUrl}/projects`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const projects = response.data;
    success(`获取到 ${projects.length} 个项目`);
    return projects;
  } catch (err) {
    error(`获取项目列表失败: ${err.message}`);
    return [];
  }
}

/**
 * 创建测试项目
 */
async function createTestProject() {
  info('创建测试项目...');
  try {
    const response = await axios.post(`${config.apiBaseUrl}/projects`, {
      name: `测试项目_${Date.now()}`,
      description: '用于测试项目管理到编辑器跳转功能的项目'
    }, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    testProject = response.data;
    success(`测试项目创建成功: ${testProject.name} (ID: ${testProject.id})`);
    return true;
  } catch (err) {
    error(`创建测试项目失败: ${err.message}`);
    return false;
  }
}

/**
 * 获取项目场景列表
 */
async function getProjectScenes(projectId) {
  info(`获取项目 ${projectId} 的场景列表...`);
  try {
    const response = await axios.get(`${config.apiBaseUrl}/projects/${projectId}/scenes`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const scenes = response.data;
    success(`获取到 ${scenes.length} 个场景`);
    return scenes;
  } catch (err) {
    error(`获取项目场景失败: ${err.message}`);
    return [];
  }
}

/**
 * 创建测试场景
 */
async function createTestScene(projectId) {
  info('创建测试场景...');
  try {
    const response = await axios.post(`${config.apiBaseUrl}/projects/${projectId}/scenes`, {
      name: `测试场景_${Date.now()}`,
      description: '用于测试的场景',
      isDefault: true
    }, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    testScene = response.data;
    success(`测试场景创建成功: ${testScene.name} (ID: ${testScene.id})`);
    return true;
  } catch (err) {
    error(`创建测试场景失败: ${err.message}`);
    return false;
  }
}

/**
 * 测试场景数据获取
 */
async function testSceneDataAccess(projectId, sceneId) {
  info(`测试场景数据获取 (项目: ${projectId}, 场景: ${sceneId})...`);
  try {
    const response = await axios.get(`${config.apiBaseUrl}/projects/${projectId}/scenes/${sceneId}/data`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const sceneData = response.data;
    success('场景数据获取成功');
    info(`场景数据结构: ${JSON.stringify(Object.keys(sceneData), null, 2)}`);
    return true;
  } catch (err) {
    error(`场景数据获取失败: ${err.message}`);
    if (err.response) {
      error(`响应状态: ${err.response.status}`);
      error(`响应数据: ${JSON.stringify(err.response.data, null, 2)}`);
    }
    return false;
  }
}

/**
 * 测试前端路由URL构造
 */
function testRouteConstruction(projectId, sceneId) {
  info('测试前端路由URL构造...');
  
  const editorUrl = `/editor/${projectId}/${sceneId}`;
  const fullUrl = `${config.frontendUrl}${editorUrl}`;
  
  success(`编辑器路由: ${editorUrl}`);
  success(`完整URL: ${fullUrl}`);
  
  return { editorUrl, fullUrl };
}

/**
 * 清理测试数据
 */
async function cleanup() {
  if (testProject) {
    info(`清理测试项目: ${testProject.id}`);
    try {
      await axios.delete(`${config.apiBaseUrl}/projects/${testProject.id}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      success('测试项目清理完成');
    } catch (err) {
      warning(`清理测试项目失败: ${err.message}`);
    }
  }
}

/**
 * 主测试流程
 */
async function runTests() {
  console.log('🚀 开始项目管理到编辑器跳转功能测试\n');

  try {
    // 1. 测试API连接
    if (!(await testApiConnection())) {
      return;
    }

    // 2. 用户登录
    if (!(await loginUser())) {
      return;
    }

    // 3. 获取现有项目
    const existingProjects = await getProjects();
    
    // 4. 创建测试项目（如果没有项目）
    if (existingProjects.length === 0) {
      if (!(await createTestProject())) {
        return;
      }
    } else {
      testProject = existingProjects[0];
      info(`使用现有项目: ${testProject.name} (ID: ${testProject.id})`);
    }

    // 5. 获取项目场景
    const scenes = await getProjectScenes(testProject.id);
    
    // 6. 创建测试场景（如果没有场景）
    if (scenes.length === 0) {
      if (!(await createTestScene(testProject.id))) {
        return;
      }
    } else {
      testScene = scenes[0];
      info(`使用现有场景: ${testScene.name} (ID: ${testScene.id})`);
    }

    // 7. 测试场景数据获取
    if (!(await testSceneDataAccess(testProject.id, testScene.id))) {
      return;
    }

    // 8. 测试路由URL构造
    const { editorUrl, fullUrl } = testRouteConstruction(testProject.id, testScene.id);

    // 9. 输出测试结果
    console.log('\n🎉 测试完成！');
    console.log('\n📋 测试结果总结:');
    success('✅ API连接正常');
    success('✅ 用户认证成功');
    success('✅ 项目数据获取正常');
    success('✅ 场景数据获取正常');
    success('✅ 路由URL构造正确');
    
    console.log('\n🔗 编辑器访问信息:');
    info(`项目ID: ${testProject.id}`);
    info(`场景ID: ${testScene.id}`);
    info(`编辑器路由: ${editorUrl}`);
    info(`完整访问URL: ${fullUrl}`);
    
    console.log('\n💡 下一步操作:');
    info('1. 在浏览器中访问 http://localhost:80');
    info('2. 登录系统');
    info('3. 在项目管理页面点击项目卡片');
    info('4. 验证是否正确跳转到编辑器界面');

  } catch (err) {
    error(`测试过程中发生错误: ${err.message}`);
  } finally {
    // 清理测试数据（可选）
    // await cleanup();
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testApiConnection,
  loginUser,
  getProjects,
  getProjectScenes,
  testSceneDataAccess
};
