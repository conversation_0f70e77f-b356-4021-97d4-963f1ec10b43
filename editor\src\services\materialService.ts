/**
 * 材质服务
 */
import { apiClient } from './ApiClient';

// 材质缓存
const materialCache = new Map<string, any>();
const cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

export const materialService = {
  /**
   * 获取所有材质
   */
  async getMaterials(params?: {
    page?: number;
    pageSize?: number;
    type?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    try {
      const response = await apiClient.get('/materials', { params });
      return response.data;
    } catch (error) {
      console.error('获取材质列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取单个材质
   */
  async getMaterial(materialId: string, useCache = true) {
    // 检查缓存
    if (useCache) {
      const cached = materialCache.get(materialId);
      if (cached) {
        return cached;
      }
    }

    try {
      const response = await apiClient.get(`/materials/${materialId}`);
      const material = response.data;

      // 更新缓存
      if (useCache) {
        materialCache.set(materialId, material);
        setTimeout(() => materialCache.delete(materialId), cacheTimeout);
      }

      return material;
    } catch (error) {
      console.error('获取材质失败:', error);
      throw error;
    }
  },

  /**
   * 创建材质
   */
  async createMaterial(materialData: any) {
    try {
      const response = await apiClient.post('/materials', {
        ...materialData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      const newMaterial = response.data;

      // 更新缓存
      materialCache.set(newMaterial.id, newMaterial);

      return newMaterial;
    } catch (error) {
      console.error('创建材质失败:', error);
      throw error;
    }
  },

  /**
   * 更新材质
   */
  async updateMaterial(materialId: string, materialData: any) {
    try {
      const response = await apiClient.patch(`/materials/${materialId}`, {
        ...materialData,
        updatedAt: new Date().toISOString()
      });

      const updatedMaterial = response.data;

      // 更新缓存
      materialCache.set(materialId, updatedMaterial);

      return updatedMaterial;
    } catch (error) {
      console.error('更新材质失败:', error);
      throw error;
    }
  },

  /**
   * 删除材质
   */
  async deleteMaterial(materialId: string) {
    try {
      await apiClient.delete(`/materials/${materialId}`);

      // 清除缓存
      materialCache.delete(materialId);
    } catch (error) {
      console.error('删除材质失败:', error);
      throw error;
    }
  },

  /**
   * 上传纹理
   */
  async uploadTexture(file: File, options?: any) {
    try {
      const response = await apiClient.upload('/materials/textures/upload', file, {
        data: options
      });
      return response.data;
    } catch (error) {
      console.error('上传纹理失败:', error);
      throw error;
    }
  },

  /**
   * 清除缓存
   */
  clearCache() {
    materialCache.clear();
  }
};