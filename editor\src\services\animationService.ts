/**
 * 动画服务
 */
import { apiClient } from './ApiClient';
import { Animation } from '../store/animations/animationsSlice';

export const animationService = {
  /**
   * 获取所有动画
   */
  async getAnimations() {
    const response = await apiClient.get('/animations');
    return response.data;
  },

  /**
   * 获取单个动画
   */
  async getAnimation(animationId: string) {
    const response = await apiClient.get(`/animations/${animationId}`);
    return response.data;
  },

  /**
   * 创建动画
   */
  async createAnimation(animationData: Omit<Animation, 'id' | 'createdAt' | 'updatedAt'>) {
    const response = await apiClient.post('/animations', animationData);
    return response.data;
  },

  /**
   * 更新动画
   */
  async updateAnimation(animationId: string, animationData: Partial<Animation>) {
    const response = await apiClient.patch(`/animations/${animationId}`, animationData);
    return response.data;
  },

  /**
   * 删除动画
   */
  async deleteAnimation(animationId: string) {
    await apiClient.delete(`/animations/${animationId}`);
  },

  /**
   * 导出动画
   */
  async exportAnimation(animationId: string) {
    const response = await apiClient.get(`/animations/${animationId}/export`);
    return response.data;
  },

  /**
   * 导入动画
   */
  async importAnimation(animationData: any) {
    const response = await apiClient.post('/animations/import', animationData);
    return response.data;
  },

  /**
   * 复制动画
   */
  async duplicateAnimation(animationId: string, newName?: string) {
    const response = await apiClient.post(`/animations/${animationId}/duplicate`, { name: newName });
    return response.data;
  },

  /**
   * 应用动画到实体
   */
  async applyAnimation(animationId: string, entityId: string) {
    const response = await apiClient.post(`/animations/${animationId}/apply`, { entityId });
    return response.data;
  }
};
